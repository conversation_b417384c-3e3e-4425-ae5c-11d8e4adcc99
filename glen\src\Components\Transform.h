#pragma once

#include "glm/vec3.hpp"
#include "glm/mat4x4.hpp"
#include "glm/glm.hpp"
#include "glm/gtx/quaternion.hpp"

#include <Component.h>

class Transform : public Component
{
public:


	glm::vec3 m_vPosition = glm::vec3(0.0f, 0.0f, 0.0f);
	glm::vec3 m_vRotation = glm::vec3(0.0f, 0.0f, 0.0f);
	glm::vec3 m_vScale = glm::vec3(1.0f, 1.0f, 1.0f);

	Transform() { name = "Transform"; }
	Transform(const Transform&) = default;
	Transform(const glm::vec3& position)
		: m_vPosition(position) {name = "Transform";}

	glm::mat4 GetLocalToWorldMatrix() const
	{
		return glm::translate(glm::mat4(1.0f), m_vPosition)
			* glm::toMat4(glm::quat(m_vRotation))
			* glm::scale(glm::mat4(1.0f), m_vScale);
	}

	glm::vec3 GetForwardVector() const
	{
		return glm::quat(m_vRotation) * glm::vec3(0, 0, -1);
	}

	glm::vec3 GetRightVector() const
	{
		return glm::normalize(glm::cross(GetForwardVector(), glm::vec3(0, 1, 0)));
	}

	glm::vec3 GetUpVector() const
	{
		return glm::normalize(glm::cross(GetRightVector(), GetForwardVector()));
	}

	void SetPosition(const glm::vec3& position) { m_vPosition = position; }
	glm::vec3 GetPosition() const { return m_vPosition; }

	void SetRotation(const glm::vec3& rotation) { m_vRotation = rotation; }
	glm::vec3 GetRotation() const { return m_vRotation; }

	void SetScale(const glm::vec3& scale) { m_vScale = scale; }
	glm::vec3 GetScale() const { return m_vScale; }

	void Translate(const glm::vec3& translation)
	{
		m_vPosition += translation;
	}

	void Rotate(const glm::vec3& rotationDegrees)
	{
		m_vRotation += glm::radians(rotationDegrees);
	}

	void Scale(const glm::vec3& scale)
	{
		m_vScale *= scale;
	}

	void Update() override;
	void Start() override;

	//Get
	std::vector<BuiltInFieldInfo> GetBuiltInFields() override;
};

