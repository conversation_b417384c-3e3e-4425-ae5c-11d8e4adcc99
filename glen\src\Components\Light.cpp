#include "Light.h"
#include "Transform.h"
#include "Camera.h"
#include <Renderer/ShadowUtils.h>
#include <Renderer/Renderer.h>

void Light::Update()
{
    
}

void Light::Start()
{
    if(m_bNeedToInitializeShadowmap)
    {
        m_bNeedToInitializeShadowmap = false;
        m_shadowMap = std::make_unique<Shadowmaps>(m_Type, m_fShadowMapSize, m_fShadowMapSize);
    }

    // Initialize ping pong buffers for lights with shadows enabled
    // For directional lights, we always need the blurred buffer for depth-to-color conversion
    if(m_bShadowEnabled && m_bNeedToInitializePingPongBuffers)
    {
        InitializePingPongBuffers();
    }

}

glm::vec3 Light::GetDirectionalLightDirection() const
{
    if (m_Type != LightType::DIRECTIONAL)
    {
        return glm::vec3(0.0f, -1.0f, 0.0f); // Default downward direction
    }

    // For directional lights, use the forward vector of the transform
    auto transform = GetGameObject()->GetTransform();
    return glm::normalize(transform->GetForwardVector());
}

glm::mat4 Light::CalculateDirectionalLightSpaceMatrix(Camera* camera, float nearPlane, float farPlane) const
{
    if (m_Type != LightType::DIRECTIONAL || !camera)
    {
        return glm::mat4(1.0f);
    }

    glm::vec3 lightDirection = GetDirectionalLightDirection();
    return ShadowUtils::CalculateFittedLightSpaceMatrix(camera, lightDirection, nearPlane, farPlane);
}

void Light::UpdateLightSpaceMatrix(Camera* camera)
{
    if (m_Type == LightType::DIRECTIONAL && camera)
    {
        // Use camera's near and far planes for shadow calculation
        float shadowNear = camera->nearClip;
        float shadowFar = camera->farClip;

        m_mLightSpaceMatrix = CalculateDirectionalLightSpaceMatrix(camera, shadowNear, shadowFar);
    }
}

void Light::InitializePingPongBuffers()
{
    m_bNeedToInitializePingPongBuffers = false;

    // Create ping pong buffers with the same size as the shadow map
    int bufferSize = static_cast<int>(m_fShadowMapSize);

    if (m_Type == LightType::DIRECTIONAL)
    {
        // For directional lights, use RG32F format for VSM
        m_pBlurredShadowBuffer = std::make_unique<FrameBuffer>(bufferSize, bufferSize, FrameBuffer::FrameBufferType::RG32F);
        m_pPingPongShadowBuffer = std::make_unique<FrameBuffer>(bufferSize, bufferSize, FrameBuffer::FrameBufferType::RG32F);
    }
    else if (m_Type == LightType::POINT)
    {
        // For point lights, use RG32F format for VSM cubemap faces
        m_pBlurredShadowBuffer = std::make_unique<FrameBuffer>(bufferSize, bufferSize, FrameBuffer::FrameBufferType::RG32F);
        m_pPingPongShadowBuffer = std::make_unique<FrameBuffer>(bufferSize, bufferSize, FrameBuffer::FrameBufferType::RG32F);
    }
}

void Light::BlurShadowMap(int blurIterations)
{
    if (!m_shadowMap || !m_bShadowEnabled) return;

    // Use the light's configured blur iterations if not specified
    if (blurIterations == -1) {
        blurIterations = m_iShadowBlurIterations;
    }

    // Initialize ping pong buffers if needed
    if (m_bNeedToInitializePingPongBuffers)
    {
        InitializePingPongBuffers();
    }

    if (m_Type == LightType::DIRECTIONAL)
    {
        // Always call for directional lights (handles depth-to-color conversion)
        // The actual blur step is conditional based on m_bSoftShadows
        BlurDirectionalShadowMap(blurIterations);
    }
    else if (m_Type == LightType::POINT)
    {
        // For point lights, skip entirely if soft shadows are disabled
        if (!m_bSoftShadows) return;
        BlurPointShadowMap(blurIterations);
    }
}

void Light::BlurDirectionalShadowMap(int blurIterations)
{
    // Always convert depth to color in the blurred shadow buffer first
    // This ensures we have a proper VSM texture even if we skip blurring
    m_pBlurredShadowBuffer->Bind();
    glViewport(0, 0, m_pBlurredShadowBuffer->GetWidth(), m_pBlurredShadowBuffer->GetHeight());
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    auto depthToColorShader = Renderer::GetShaderManager()->GetShader("depth_to_color");
    depthToColorShader->Bind();
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, m_shadowMap->getShadowMapTexture());
    depthToColorShader->SetInt(0, "depthTex");
    glBindVertexArray(Renderer::quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);
    glBindTexture(GL_TEXTURE_2D, 0);
    depthToColorShader->Unbind();
    m_pBlurredShadowBuffer->Unbind();

    // If soft shadows are disabled, skip the blur process
    if (!m_bSoftShadows) {
        return;
    }

    // Ping pong blur
    bool horizontal = false;
    FrameBuffer* readBuffer = m_pBlurredShadowBuffer.get();
    FrameBuffer* writeBuffer = m_pPingPongShadowBuffer.get();

    for (int i = 0; i < blurIterations; ++i)
    {
        writeBuffer->Bind();
        glViewport(0, 0, writeBuffer->GetWidth(), writeBuffer->GetHeight());
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        auto bilateralShader = Renderer::GetShaderManager()->GetShader("bilateral_vsm");
        bilateralShader->Bind();
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, readBuffer->GetTextureID());
        bilateralShader->SetInt(0, "vsmTexture");
        bilateralShader->SetBool(horizontal, "horizontal");

        // Set bilateral blur parameters
        bilateralShader->SetFloat(0.01f, "depthThreshold"); // Adjust this to control edge preservation
        bilateralShader->SetFloat(1.5f, "blurRadius"); // Adjust this to control blur amount

        glBindVertexArray(Renderer::quadVAO);
        glDrawArrays(GL_TRIANGLES, 0, 6);
        glBindVertexArray(0);
        glBindTexture(GL_TEXTURE_2D, 0);
        bilateralShader->Unbind();
        writeBuffer->Unbind();

        // Swap buffers for next pass
        std::swap(readBuffer, writeBuffer);
        horizontal = !horizontal;
    }

    // The final result is in readBuffer (which was swapped)
    // Copy the final result back to the blurred shadow buffer for consistent access
    if (readBuffer != m_pBlurredShadowBuffer.get())
    {
        // Copy from ping pong buffer back to blurred buffer
        m_pBlurredShadowBuffer->Bind();
        glViewport(0, 0, m_pBlurredShadowBuffer->GetWidth(), m_pBlurredShadowBuffer->GetHeight());
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        auto copyShader = Renderer::GetShaderManager()->GetShader("framebuffer");
        copyShader->Bind();
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, readBuffer->GetTextureID());
        glBindVertexArray(Renderer::quadVAO);
        glDrawArrays(GL_TRIANGLES, 0, 6);
        glBindVertexArray(0);
        glBindTexture(GL_TEXTURE_2D, 0);
        copyShader->Unbind();
        m_pBlurredShadowBuffer->Unbind();
    }
}

void Light::BlurPointShadowMap(int blurIterations)
{
    // For point lights, we need to blur each face of the cubemap individually
    GLuint originalCubemap = m_shadowMap->getShadowMapTexture();

    // Cubemap face targets
    GLenum faces[6] = {
        GL_TEXTURE_CUBE_MAP_POSITIVE_X, GL_TEXTURE_CUBE_MAP_NEGATIVE_X,
        GL_TEXTURE_CUBE_MAP_POSITIVE_Y, GL_TEXTURE_CUBE_MAP_NEGATIVE_Y,
        GL_TEXTURE_CUBE_MAP_POSITIVE_Z, GL_TEXTURE_CUBE_MAP_NEGATIVE_Z
    };

    int faceSize = static_cast<int>(m_fShadowMapSize);

    // Create a temporary framebuffer for reading cubemap faces
    GLuint tempFBO;
    glGenFramebuffers(1, &tempFBO);

    // Process each face of the cubemap
    for (int face = 0; face < 6; ++face)
    {
        // Step 1: Extract the cubemap face to our blur buffer
        // Bind the cubemap face as a framebuffer attachment for reading
        glBindFramebuffer(GL_READ_FRAMEBUFFER, tempFBO);
        glFramebufferTexture2D(GL_READ_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, faces[face], originalCubemap, 0);

        // Bind our blur buffer as the draw target
        glBindFramebuffer(GL_DRAW_FRAMEBUFFER, m_pBlurredShadowBuffer->GetFBO());

        // Copy the cubemap face to the 2D texture
        glBlitFramebuffer(0, 0, faceSize, faceSize, 0, 0, faceSize, faceSize, GL_COLOR_BUFFER_BIT, GL_NEAREST);

        // Step 2: Blur the extracted face using ping pong
        FrameBuffer* readBuffer = m_pBlurredShadowBuffer.get();
        FrameBuffer* writeBuffer = m_pPingPongShadowBuffer.get();
        bool horizontal = false;

        for (int i = 0; i < blurIterations; ++i)
        {
            writeBuffer->Bind();
            glViewport(0, 0, faceSize, faceSize);
            glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

            auto bilateralShader = Renderer::GetShaderManager()->GetShader("bilateral_vsm");
            bilateralShader->Bind();
            glActiveTexture(GL_TEXTURE0);
            glBindTexture(GL_TEXTURE_2D, readBuffer->GetTextureID());
            bilateralShader->SetInt(0, "vsmTexture");
            bilateralShader->SetBool(horizontal, "horizontal");

            // Set bilateral blur parameters for point lights (more aggressive edge preservation)
            bilateralShader->SetFloat(0.005f, "depthThreshold"); // Tighter threshold for sharper edges
            bilateralShader->SetFloat(1.0f, "blurRadius"); // Smaller radius to reduce stepping

            glBindVertexArray(Renderer::quadVAO);
            glDrawArrays(GL_TRIANGLES, 0, 6);
            glBindVertexArray(0);
            glBindTexture(GL_TEXTURE_2D, 0);
            bilateralShader->Unbind();
            writeBuffer->Unbind();

            std::swap(readBuffer, writeBuffer);
            horizontal = !horizontal;
        }

        // Step 3: Copy the blurred result back to the cubemap face
        glBindFramebuffer(GL_READ_FRAMEBUFFER, readBuffer->GetFBO());
        glBindFramebuffer(GL_DRAW_FRAMEBUFFER, tempFBO);
        glFramebufferTexture2D(GL_DRAW_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, faces[face], originalCubemap, 0);
        glBlitFramebuffer(0, 0, faceSize, faceSize, 0, 0, faceSize, faceSize, GL_COLOR_BUFFER_BIT, GL_NEAREST);
    }

    // Clean up
    glDeleteFramebuffers(1, &tempFBO);
    glBindFramebuffer(GL_FRAMEBUFFER, 0);

}

GLuint Light::GetBlurredShadowMapTexture() const
{
    if (m_Type == LightType::DIRECTIONAL)
    {
        // For directional lights, always return the blurred buffer if it exists
        // The buffer contains either blurred or unblurred VSM data depending on m_bSoftShadows
        if (m_pBlurredShadowBuffer)
        {
            return m_pBlurredShadowBuffer->GetTextureID();
        }
        // Fallback to original shadow map (though this shouldn't happen in normal operation)
        else if (m_shadowMap)
        {
            return m_shadowMap->getShadowMapTexture();
        }
    }
    else if (m_Type == LightType::POINT && m_shadowMap)
    {
        // For point lights, the cubemap is blurred in-place if soft shadows are enabled
        // Return the same texture regardless (it's either blurred or not based on m_bSoftShadows)
        return m_shadowMap->getShadowMapTexture();
    }
    return 0;
}