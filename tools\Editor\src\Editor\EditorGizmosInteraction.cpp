#include "EditorGizmos.h" // Make sure this header declares EditorGizmos and GizmoAxis
#include "Editor.h"
#include <Components/Light.h>
#include <Components/MeshRenderer.h>
#include <imgui.h>
#include <iostream>

namespace Editor {

// Helper function to project mouse movement onto a world plane
glm::vec3 ProjectMouseToPlane(const glm::vec2& mouseDelta, const glm::vec3& objectPos,
                             const glm::vec3& planeNormal, const glm::mat4& view,
                             const glm::mat4& proj, float scaleFactor)
{
    // Convert mouse delta from screen space to normalized device coordinates
    glm::vec2 ndcDelta = glm::vec2(
        mouseDelta.x / (EditorGizmos::s_sceneViewWidth * 0.5f),
        -mouseDelta.y / (EditorGizmos::s_sceneViewHeight * 0.5f)  // Flip Y
    );

    // Get camera position
    glm::mat4 invView = glm::inverse(view);
    glm::vec3 cameraPos = glm::vec3(invView[3]);

    // Create rays for start and end mouse positions
    glm::mat4 invProj = glm::inverse(proj);
    glm::mat4 invViewProj = glm::inverse(proj * view);

    // Ray from camera through original mouse position
    glm::vec4 rayStart_ndc = glm::vec4(0.0f, 0.0f, -1.0f, 1.0f);
    glm::vec4 rayEnd_ndc = glm::vec4(0.0f, 0.0f, 1.0f, 1.0f);

    // Ray from camera through new mouse position
    glm::vec4 rayStartNew_ndc = glm::vec4(ndcDelta.x, ndcDelta.y, -1.0f, 1.0f);
    glm::vec4 rayEndNew_ndc = glm::vec4(ndcDelta.x, ndcDelta.y, 1.0f, 1.0f);

    // Transform to world space
    glm::vec4 rayStart_world = invViewProj * rayStart_ndc;
    glm::vec4 rayEnd_world = invViewProj * rayEnd_ndc;
    glm::vec4 rayStartNew_world = invViewProj * rayStartNew_ndc;
    glm::vec4 rayEndNew_world = invViewProj * rayEndNew_ndc;

    rayStart_world /= rayStart_world.w;
    rayEnd_world /= rayEnd_world.w;
    rayStartNew_world /= rayStartNew_world.w;
    rayEndNew_world /= rayEndNew_world.w;

    // Calculate ray directions
    glm::vec3 rayDir = glm::normalize(glm::vec3(rayEnd_world - rayStart_world));
    glm::vec3 rayDirNew = glm::normalize(glm::vec3(rayEndNew_world - rayStartNew_world));

    // Intersect both rays with the plane
    float denom = glm::dot(planeNormal, rayDir);
    float denomNew = glm::dot(planeNormal, rayDirNew);

    if (abs(denom) < 0.0001f || abs(denomNew) < 0.0001f) {
        // Ray is parallel to plane, fallback to simple movement
        return glm::vec3(0.0f);
    }

    // Calculate intersection points
    float t = glm::dot(objectPos - cameraPos, planeNormal) / denom;
    float tNew = glm::dot(objectPos - cameraPos, planeNormal) / denomNew;

    glm::vec3 intersectionPoint = cameraPos + rayDir * t;
    glm::vec3 intersectionPointNew = cameraPos + rayDirNew * tNew;

    // Return the movement vector
    return intersectionPointNew - intersectionPoint;
}

void EditorGizmos::HandleGizmoInteraction(Camera* camera)
{
    // Only handle when an object is selected and scene is hovered
    if (s_selectedGameObjectIndex < 0) {
        return;
    }

    if (!EditorState::GetInstance().bIsSceneEditorHovered) {
        return;
    }
    
    auto scene = EditorState::GetInstance().GetActiveScene();
    if (!scene) return;
    
    auto gameObjects = scene->GetGameObjects();
    if (s_selectedGameObjectIndex >= static_cast<int>(gameObjects.size())) return;
    
    // Check for mouse press on gizmo arrows
    if (ImGui::IsMouseDown(0))
    {
       
        std::cout << "Mouse clicked, checking gizmo interaction... Selected object: "
                  << s_selectedGameObjectIndex << ", Scene hovered: "
                  << EditorState::GetInstance().bIsSceneEditorHovered << std::endl;
        ImVec2 mousePos = ImGui::GetMousePos();
        
        // Convert mouse position to scene view relative coordinates
        float relativeX = mousePos.x - s_sceneViewX;
        float relativeY = mousePos.y - s_sceneViewY;
        
        // Check if mouse is within scene view bounds
        if (relativeX < 0 || relativeX > s_sceneViewWidth || 
            relativeY < 0 || relativeY > s_sceneViewHeight) {
            return;
        }

        // Ensure we have a valid picking framebuffer
        if (!s_pickingFrameBuffer) {
            std::cout << "Error: Picking framebuffer is null!" << std::endl;
            return;
        }

        s_pickingFrameBuffer->Bind();

        // Convert to framebuffer coordinates (flip Y)
        int pixelX = static_cast<int>(relativeX);
        int pixelY = static_cast<int>(s_sceneViewHeight - relativeY); // Flip Y coordinate

        // Clamp coordinates to framebuffer bounds
        pixelX = std::max(0, std::min(pixelX, static_cast<int>(s_sceneViewWidth) - 1));
        pixelY = std::max(0, std::min(pixelY, static_cast<int>(s_sceneViewHeight) - 1));

        unsigned char pixel[4];
        glReadPixels(pixelX, pixelY, 1, 1, GL_RGBA, GL_UNSIGNED_BYTE, pixel);

        s_pickingFrameBuffer->Unbind();

        // Get axis from color
        GizmoAxis hitAxis = GetAxisFromPickingColor(pixel[0], pixel[1], pixel[2]);

        if (hitAxis != GizmoAxis::NONE)
        {
            // Start dragging
            s_activeGizmoAxis = hitAxis;
            s_isDragging = true;
            s_dragStartPos = glm::vec3(relativeX, relativeY, 0.0f);
            s_objectStartPos = gameObjects[s_selectedGameObjectIndex]->GetTransform()->GetPosition();

            return; // Important: return early to prevent object selection
        }
        else
        {
            // Only reset if we're not currently dragging
            if (!s_isDragging)
            {
                s_activeGizmoAxis = GizmoAxis::NONE;
            }
        }
    }
}

void EditorGizmos::UpdateGizmoDrag(Camera* camera)
{
    if (!s_isDragging || s_activeGizmoAxis == EditorGizmos::GizmoAxis::NONE) return;

    // Check for mouse release
    if (!ImGui::IsMouseDown(0))
    {
        s_isDragging = false;
        s_activeGizmoAxis = EditorGizmos::GizmoAxis::NONE;
        return;
    }

    auto scene = EditorState::GetInstance().GetActiveScene();
    if (!scene || s_selectedGameObjectIndex < 0) return;

    auto gameObjects = scene->GetGameObjects();
    if (s_selectedGameObjectIndex >= static_cast<int>(gameObjects.size())) return;

    ImVec2 mousePos = ImGui::GetMousePos();
    float relativeX = mousePos.x - s_sceneViewX;
    float relativeY = mousePos.y - s_sceneViewY;

    // Calculate mouse delta in screen space
    glm::vec2 currentMousePos = glm::vec2(relativeX, relativeY);
    glm::vec2 startMousePos = glm::vec2(s_dragStartPos.x, s_dragStartPos.y);
    glm::vec2 mouseDelta = currentMousePos - startMousePos;

    // Get camera matrices
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);

    float aspectRatio = s_sceneViewWidth / s_sceneViewHeight;
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);

    // Get the object's current position
    auto selectedObject = gameObjects[s_selectedGameObjectIndex];
    glm::vec3 objectPos = s_objectStartPos;

    // Define world space axis vectors
    glm::vec3 worldAxisX = glm::vec3(1.0f, 0.0f, 0.0f);
    glm::vec3 worldAxisY = glm::vec3(0.0f, 1.0f, 0.0f);
    glm::vec3 worldAxisZ = glm::vec3(0.0f, 0.0f, 1.0f);

    // Calculate distance from camera to object for depth-based scaling
    float distanceToObject = glm::length(objectPos - cameraPos);
    float scaleFactor = distanceToObject * 0.001f; // Adjust this multiplier as needed

    glm::vec3 worldMovement = glm::vec3(0.0f);

    switch (s_activeGizmoAxis)
    {
        case EditorGizmos::GizmoAxis::X:
        {
            // Single axis movement along X
            glm::vec4 objectScreenPos = proj * view * glm::vec4(objectPos, 1.0f);
            objectScreenPos /= objectScreenPos.w;
            glm::vec4 axisEndScreenPos = proj * view * glm::vec4(objectPos + worldAxisX, 1.0f);
            axisEndScreenPos /= axisEndScreenPos.w;

            glm::vec2 objectScreen = glm::vec2(
                (objectScreenPos.x + 1.0f) * 0.5f * s_sceneViewWidth,
                (1.0f - objectScreenPos.y) * 0.5f * s_sceneViewHeight
            );
            glm::vec2 axisEndScreen = glm::vec2(
                (axisEndScreenPos.x + 1.0f) * 0.5f * s_sceneViewWidth,
                (1.0f - axisEndScreenPos.y) * 0.5f * s_sceneViewHeight
            );

            glm::vec2 screenAxisDir = glm::normalize(axisEndScreen - objectScreen);
            float projectedMovement = glm::dot(mouseDelta, screenAxisDir);
            worldMovement = worldAxisX * projectedMovement * scaleFactor;
            break;
        }
        case EditorGizmos::GizmoAxis::Y:
        {
            // Single axis movement along Y
            glm::vec4 objectScreenPos = proj * view * glm::vec4(objectPos, 1.0f);
            objectScreenPos /= objectScreenPos.w;
            glm::vec4 axisEndScreenPos = proj * view * glm::vec4(objectPos + worldAxisY, 1.0f);
            axisEndScreenPos /= axisEndScreenPos.w;

            glm::vec2 objectScreen = glm::vec2(
                (objectScreenPos.x + 1.0f) * 0.5f * s_sceneViewWidth,
                (1.0f - objectScreenPos.y) * 0.5f * s_sceneViewHeight
            );
            glm::vec2 axisEndScreen = glm::vec2(
                (axisEndScreenPos.x + 1.0f) * 0.5f * s_sceneViewWidth,
                (1.0f - axisEndScreenPos.y) * 0.5f * s_sceneViewHeight
            );

            glm::vec2 screenAxisDir = glm::normalize(axisEndScreen - objectScreen);
            float projectedMovement = glm::dot(mouseDelta, screenAxisDir);
            worldMovement = worldAxisY * projectedMovement * scaleFactor;
            break;
        }
        case EditorGizmos::GizmoAxis::Z:
        {
            // Single axis movement along Z
            glm::vec4 objectScreenPos = proj * view * glm::vec4(objectPos, 1.0f);
            objectScreenPos /= objectScreenPos.w;
            glm::vec4 axisEndScreenPos = proj * view * glm::vec4(objectPos + worldAxisZ, 1.0f);
            axisEndScreenPos /= axisEndScreenPos.w;

            glm::vec2 objectScreen = glm::vec2(
                (objectScreenPos.x + 1.0f) * 0.5f * s_sceneViewWidth,
                (1.0f - objectScreenPos.y) * 0.5f * s_sceneViewHeight
            );
            glm::vec2 axisEndScreen = glm::vec2(
                (axisEndScreenPos.x + 1.0f) * 0.5f * s_sceneViewWidth,
                (1.0f - axisEndScreenPos.y) * 0.5f * s_sceneViewHeight
            );

            glm::vec2 screenAxisDir = glm::normalize(axisEndScreen - objectScreen);
            float projectedMovement = glm::dot(mouseDelta, screenAxisDir);
            worldMovement = worldAxisZ * projectedMovement * scaleFactor;
            break;
        }
        case EditorGizmos::GizmoAxis::XY:
        {
            // Project mouse movement onto XY plane as seen from camera
            glm::vec3 planeNormal = worldAxisZ; // XY plane normal is Z
            worldMovement = ProjectMouseToPlane(mouseDelta, objectPos, planeNormal, view, proj, scaleFactor);
            break;
        }
        case EditorGizmos::GizmoAxis::XZ:
        {
            // Project mouse movement onto XZ plane as seen from camera
            glm::vec3 planeNormal = worldAxisY; // XZ plane normal is Y
            worldMovement = ProjectMouseToPlane(mouseDelta, objectPos, planeNormal, view, proj, scaleFactor);
            break;
        }
        case EditorGizmos::GizmoAxis::YZ:
        {
            // Project mouse movement onto YZ plane as seen from camera
            glm::vec3 planeNormal = worldAxisX; // YZ plane normal is X
            worldMovement = ProjectMouseToPlane(mouseDelta, objectPos, planeNormal, view, proj, scaleFactor);
            break;
        }
        default:
            return;
    }

    // Update object position
    glm::vec3 newPosition = s_objectStartPos + worldMovement;
    selectedObject->GetTransform()->SetPosition(newPosition);
}

} // namespace Editor
