#include "EditorGizmos.h"
#include "Editor.h"
#include <Components/Light.h>
#include <Components/MeshRenderer.h>

#include <imgui.h>

namespace Editor {

// Simple AABB frustum check for a sphere centered at 'center' with radius 'radius'
static bool IsBoxInFrustum(const glm::vec3& center, float radius, const Camera::FrustumCorners& frustum)
{
    // Simple distance check from camera center - if object is too far, cull it
    float maxDistance = glm::length(frustum.farTopLeft - frustum.nearTopLeft) * 0.5f;
    float distanceFromCenter = glm::length(center - frustum.center);
    return distanceFromCenter <= maxDistance + radius;
}

void EditorGizmos::RenderLightGizmos(Camera* camera)
{
    // Only render light gizmos for the selected object
    auto& editorState = EditorState::GetInstance();
    auto selectedObject = editorState.GetActiveGameObject();
    if (!selectedObject) return;

    // Check if selected object has a light component
    auto lightComponent = selectedObject->GetComponent<Light>();
    if (!lightComponent) return; // No light component, no gizmos to render

    // Calculate view and projection matrices
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);

    float aspectRatio = (float)camera->m_pFrameBuffer->GetWidth() / (float)camera->m_pFrameBuffer->GetHeight();
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);

    auto gizmoShader = Renderer::GetShaderManager()->GetShader("gizmos");
    if (!gizmoShader) return;

    gizmoShader->Bind();

    // Render light gizmos only for the selected light
    glm::vec3 pos = lightComponent->GetGameObject()->GetTransform()->GetPosition();

    // Only render range gizmo for point lights
    if (lightComponent->m_Type == Light::LightType::POINT)
    {
        float radius = lightComponent->m_fRange;
        glm::mat4 model = glm::translate(glm::mat4(1.0f), pos) * glm::scale(glm::mat4(1.0f), glm::vec3(radius));
        glm::mat4 mvp = proj * view * model;
        gizmoShader->SetMat4(mvp, "uMVP");
        gizmoShader->SetVec3(lightComponent->m_vColor, "mainColor");
        gizmoShader->SetBool(false, "isSolid");
        glBindVertexArray(s_wireSphere.vao);
        glDrawElements(GL_LINES, s_wireSphere.indexCount, GL_UNSIGNED_INT, 0);
    }

    glBindVertexArray(0);
    gizmoShader->Unbind();
}

void EditorGizmos::RenderTransformGizmos(Camera* camera)
{
    auto& editorState = EditorState::GetInstance();
    auto selectedObject = editorState.GetActiveGameObject();
    if (!selectedObject) return;
    
    // Calculate view and projection matrices
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    
    float aspectRatio = (float)camera->m_pFrameBuffer->GetWidth() / (float)camera->m_pFrameBuffer->GetHeight();
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);
    
    auto gizmoShader = Renderer::GetShaderManager()->GetShader("gizmos");
    if (!gizmoShader) return;

    gizmoShader->Bind();

    // Disable backface culling so gizmos render properly from all angles
    glDisable(GL_CULL_FACE);

    auto transform = selectedObject->GetTransform();
    glm::vec3 pos = transform->GetPosition();

    // Get hover state by checking what's under the mouse cursor
    GizmoAxis hoveredAxis = GetHoveredAxis(camera);

    // Draw forward arrow (red, along -Z for OpenGL convention)
    glm::mat4 model = glm::translate(glm::mat4(1.0f), pos)
          * glm::rotate(glm::mat4(1.0f), glm::radians(-90.0f), glm::vec3(1, 0, 0)); // +Y to -Z
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    // Brighten color if hovered
    glm::vec3 zColor = (hoveredAxis == GizmoAxis::Z) ? glm::vec3(0.5f, 0.5f, 1.0f) : glm::vec3(0.0f, 0.0f, 1.0f);
    gizmoShader->SetVec3(zColor, "mainColor");
    glBindVertexArray(s_wireArrow.vao);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboLines);
    glDrawElements(GL_LINES, s_wireArrow.lineIndexCount, GL_UNSIGNED_INT, 0);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboTris);
    gizmoShader->SetBool(true, "isSolid");
    glDrawElements(GL_TRIANGLES, s_wireArrow.triIndexCount, GL_UNSIGNED_INT, 0);
    gizmoShader->SetBool(false, "isSolid");

    // Draw up arrow (green, along +Y)
    model = glm::translate(glm::mat4(1.0f), pos);
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    // Brighten color if hovered
    glm::vec3 yColor = (hoveredAxis == GizmoAxis::Y) ? glm::vec3(0.5f, 1.0f, 0.5f) : glm::vec3(0.0f, 1.0f, 0.0f);
    gizmoShader->SetVec3(yColor, "mainColor");
    glBindVertexArray(s_wireArrow.vao);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboLines);
    glDrawElements(GL_LINES, s_wireArrow.lineIndexCount, GL_UNSIGNED_INT, 0);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboTris);
    gizmoShader->SetBool(true, "isSolid");
    glDrawElements(GL_TRIANGLES, s_wireArrow.triIndexCount, GL_UNSIGNED_INT, 0);
    gizmoShader->SetBool(false, "isSolid");

    // Draw right arrow (blue, along +X)
    model = glm::translate(glm::mat4(1.0f), pos)
          * glm::rotate(glm::mat4(1.0f), glm::radians(-90.0f), glm::vec3(0, 0, 1)); // +Y to +X
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    // Brighten color if hovered
    glm::vec3 xColor = (hoveredAxis == GizmoAxis::X) ? glm::vec3(1.0f, 0.5f, 0.5f) : glm::vec3(1.0f, 0.0f, 0.0f);
    gizmoShader->SetVec3(xColor, "mainColor");
    glBindVertexArray(s_wireArrow.vao);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboLines);
    glDrawElements(GL_LINES, s_wireArrow.lineIndexCount, GL_UNSIGNED_INT, 0);
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_wireArrow.eboTris);
    gizmoShader->SetBool(true, "isSolid");
    glDrawElements(GL_TRIANGLES, s_wireArrow.triIndexCount, GL_UNSIGNED_INT, 0);
    gizmoShader->SetBool(false, "isSolid");

    // Disable depth testing for plane handles to avoid depth ordering issues
    glDisable(GL_DEPTH_TEST);

    // Enable blending for transparent plane handles
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    // Render plane handles for multi-axis movement at the gizmo origin
    // XY plane (light gray with 50% opacity, brighten if hovered)
    model = glm::translate(glm::mat4(1.0f), pos);
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    glm::vec3 xyPlaneColor = (hoveredAxis == GizmoAxis::XY) ? glm::vec3(0.0f, 0.9f, 0.9f) : glm::vec3(0.7f, 0.7f, 0.7f);
    float xyPlaneAlpha = (hoveredAxis == GizmoAxis::XY) ? 0.8f : 0.5f;
    gizmoShader->SetVec3(xyPlaneColor, "mainColor");
    gizmoShader->SetFloat(xyPlaneAlpha, "alpha");
    gizmoShader->SetBool(true, "isSolid");
    glBindVertexArray(s_plane.vao);
    glDrawElements(GL_TRIANGLES, s_plane.indexCount, GL_UNSIGNED_INT, 0);

    // Quad outline for XY plane using GL_LINE_LOOP
    glm::vec3 xyOutlineColor = (hoveredAxis == GizmoAxis::XY) ? glm::vec3(0.2f, 1.0f, 1.0f) : glm::vec3(0.9f, 0.9f, 0.9f);
    gizmoShader->SetVec3(xyOutlineColor, "mainColor");
    gizmoShader->SetFloat(1.0f, "alpha"); // Full opacity for outline
    gizmoShader->SetBool(false, "isSolid");
    glDrawArrays(GL_LINE_LOOP, 0, 4); // Draw quad outline

    // XZ plane (light gray with 50% opacity, brighten if hovered)
    model = glm::translate(glm::mat4(1.0f), pos)
          * glm::rotate(glm::mat4(1.0f), glm::radians(-90.0f), glm::vec3(1, 0, 0)); // Rotate to XZ plane
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    glm::vec3 xzPlaneColor = (hoveredAxis == GizmoAxis::XZ) ? glm::vec3(0.9f, 0.0f, 0.9f) : glm::vec3(0.7f, 0.7f, 0.7f);
    float xzPlaneAlpha = (hoveredAxis == GizmoAxis::XZ) ? 0.8f : 0.5f;
    gizmoShader->SetVec3(xzPlaneColor, "mainColor");
    gizmoShader->SetFloat(xzPlaneAlpha, "alpha");
    gizmoShader->SetBool(true, "isSolid");
    glDrawElements(GL_TRIANGLES, s_plane.indexCount, GL_UNSIGNED_INT, 0);

    // Quad outline for XZ plane using GL_LINE_LOOP
    glm::vec3 xzOutlineColor = (hoveredAxis == GizmoAxis::XZ) ? glm::vec3(1.0f, 0.2f, 1.0f) : glm::vec3(0.9f, 0.9f, 0.9f);
    gizmoShader->SetVec3(xzOutlineColor, "mainColor");
    gizmoShader->SetFloat(1.0f, "alpha"); // Full opacity for outline
    gizmoShader->SetBool(false, "isSolid");
    glDrawArrays(GL_LINE_LOOP, 0, 4); // Draw quad outline

    // YZ plane (light gray with 50% opacity, brighten if hovered)
    model = glm::translate(glm::mat4(1.0f), pos)
          * glm::rotate(glm::mat4(1.0f), glm::radians(90.0f), glm::vec3(0, 1, 0)); // Rotate to YZ plane
    gizmoShader->SetMat4(proj * view * model, "uMVP");
    glm::vec3 yzPlaneColor = (hoveredAxis == GizmoAxis::YZ) ? glm::vec3(0.9f, 0.9f, 0.0f) : glm::vec3(0.7f, 0.7f, 0.7f);
    float yzPlaneAlpha = (hoveredAxis == GizmoAxis::YZ) ? 0.8f : 0.5f;
    gizmoShader->SetVec3(yzPlaneColor, "mainColor");
    gizmoShader->SetFloat(yzPlaneAlpha, "alpha");
    gizmoShader->SetBool(true, "isSolid");
    glDrawElements(GL_TRIANGLES, s_plane.indexCount, GL_UNSIGNED_INT, 0);

    // Quad outline for YZ plane using GL_LINE_LOOP
    glm::vec3 yzOutlineColor = (hoveredAxis == GizmoAxis::YZ) ? glm::vec3(1.0f, 1.0f, 0.2f) : glm::vec3(0.9f, 0.9f, 0.9f);
    gizmoShader->SetVec3(yzOutlineColor, "mainColor");
    gizmoShader->SetFloat(1.0f, "alpha"); // Full opacity for outline
    gizmoShader->SetBool(false, "isSolid");
    glDrawArrays(GL_LINE_LOOP, 0, 4); // Draw quad outline

    // Disable blending and re-enable depth testing
    glDisable(GL_BLEND);
    glEnable(GL_DEPTH_TEST);

    glBindVertexArray(0);

    // Re-enable backface culling
    glEnable(GL_CULL_FACE);

    gizmoShader->Unbind();
}

void EditorGizmos::RenderHitPointGizmo(Camera* camera)
{
    if (!s_hasHit) return;

    // Calculate view and projection matrices
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);

    float aspectRatio = (float)camera->m_pFrameBuffer->GetWidth() / (float)camera->m_pFrameBuffer->GetHeight();
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);

    auto gizmoShader = Renderer::GetShaderManager()->GetShader("gizmos");
    if (!gizmoShader) return;

    gizmoShader->Bind();

    // Render solid green cube at hit point for debugging
    glm::mat4 model = glm::translate(glm::mat4(1.0f), s_hitPoint) * glm::scale(glm::mat4(1.0f), glm::vec3(0.3f));
    glm::mat4 mvp = proj * view * model;
    gizmoShader->SetMat4(mvp, "uMVP");
    gizmoShader->SetVec3(glm::vec3(0.0f, 1.0f, 0.0f), "mainColor"); // Green
    gizmoShader->SetBool(true, "isSolid"); // Solid cube
    glBindVertexArray(s_wireBox.vao);
    glDrawElements(GL_TRIANGLES, s_wireBox.indexCount, GL_UNSIGNED_INT, 0);

    glBindVertexArray(0);
    gizmoShader->Unbind();
}

void EditorGizmos::RenderBillboards(Camera* camera)
{
    auto scene = EditorState::GetInstance().GetActiveScene();
    if (!scene) return;

    // Calculate view and projection matrices
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::vec3 cameraRight = camera->GetGameObject()->GetTransform()->GetRightVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);

    float aspectRatio = (float)camera->m_pFrameBuffer->GetWidth() / (float)camera->m_pFrameBuffer->GetHeight();
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);

    // Calculate frustum planes for culling
    float fovRad = glm::radians(camera->fov);
    float halfVSide = camera->farClip * tanf(fovRad * 0.5f);
    float halfHSide = halfVSide * aspectRatio;
    glm::vec3 frontMultFar = camera->farClip * cameraFront;

    // Render billboards for all GameObjects (but not for selected objects - they get full gizmos)
    auto gameObjects = scene->GetGameObjects();

    auto& editorState = EditorState::GetInstance();
    auto selectedObject = editorState.GetActiveGameObject();

    int billboardsRendered = 0;
    for (size_t i = 0; i < gameObjects.size(); ++i)
    {
        GLuint textureID = 0;
        auto& gameObject = gameObjects[i];

        // Skip rendering billboard for selected object (it will get full transform gizmos instead)
        if (gameObject == selectedObject)
        {
            continue;
        }
        glm::vec3 position = gameObject->GetTransform()->GetPosition();

        Camera::FrustumCorners frustum = camera->GetFrustumCorners(camera->nearClip, camera->farClip);
        if (!IsBoxInFrustum(position, 1.0f, frustum))
        {
            continue;
        }
        
        // Choose color based on object components
        glm::vec3 billboardColor = glm::vec3(1.0f, 1.0f, 1.0f); // Default white

        if (gameObject->GetComponent<Light>())
        {
            // Yellow for lights
            billboardColor = glm::vec3(1.0f, 1.0f, 1.0f);
            textureID = Renderer::GetTextureManager()->GetTexture("light")->GetTextureID();
        }
        else if (gameObject->GetComponent<MeshRenderer>())
        {
            // Cyan for mesh renderers
            billboardColor = glm::vec3(1.0f, 1.0f, 1.0f);
        }
        else if (gameObject->GetComponent<Sound>())
        {
            // Magenta for audio sources
            billboardColor = glm::vec3(1.0f, 1.0f, 1.0f);
            textureID = Renderer::GetTextureManager()->GetTexture("audio")->GetTextureID();
        }
        else
        {
            // White for other objects
            billboardColor = glm::vec3(1.0f, 1.0f, 1.0f);
            textureID = Renderer::GetTextureManager()->GetTexture("gameobject")->GetTextureID();
        }

        RenderBillboard(position, 0.8f, view, proj, billboardColor, 1.0f, textureID);
        billboardsRendered++;
    }
}

} // namespace Editor



