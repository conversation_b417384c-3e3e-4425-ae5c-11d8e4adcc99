#include "Scene.h"
#include "GameObject.h"
#include "Component.h"
#include "Components/Transform.h"
#include <debug.h>

GameObject::GameObject()
{
    auto t = std::make_shared<Transform>();
    transform = AddComponent(t);

    transform = GetComponent<Transform>();
    if (transform == nullptr)
    {
        printf("GameObject::GameObject() - Transform component not found!\n");
    }

    //TODO: implement something like SceneManager::GetInstance().GetCurrentScene()->AddGameObject(this);
}

GameObject::GameObject(const std::string name) : GameObject()
{
    this->name = name;
}

GameObject::~GameObject()
{
    Debug::Log(ReturnTypes::SUCCESS, "GameObject::~GameObject() - GameObject destroyed");
    // Clear all components and children
    components.clear();
    icomponents.clear();
    children.clear();
}

void GameObject::Update()
{
    // Update all components of the current GameObject
    for (const auto& component : icomponents)
    {
        component->Update();
    }

    for (const auto& component : components)
    {
        component->Update();
    }

    // Update all child GameObjects (which will also update their components)
    for (const auto& child : children)
    {
        child->Update();
    }
}

std::shared_ptr<Component> GameObject::AddComponent(std::shared_ptr<Component> component)
{
    if (component == nullptr)
        return nullptr;

    component->SetGameObject(*this);
    component->Start();
    this->components.push_back(component);

    return component;
}

std::shared_ptr<IComponent> GameObject::AddComponent(std::shared_ptr<IComponent> component)
{
    if (component == nullptr)
        return nullptr;

    component->SetGameObject(this);
    component->Start();
    this->icomponents.push_back(component);

    return component;
}

std::shared_ptr<GameObject> GameObject::AddChild(std::shared_ptr<GameObject> child)
{
    if (child == nullptr)
        return nullptr;

    child->SetParent(shared_from_this());
    children.push_back(child);
    return child;
}

void GameObject::Start()
{

    //set go for all components
    for (const auto& component : components)
    {
        component->SetGameObject(*this);
    }
    
    // Start all components of the current GameObject
    for (const auto& component : components)
    {
        component->Start();
    }

    for (const auto& component : icomponents)
    {
        component->Start();
    }


    // Start all child GameObjects (which will also start their components)
    for (const auto& child : children)
    {
        child->Start();
    }
}