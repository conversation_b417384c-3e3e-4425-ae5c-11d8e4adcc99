#include "EditorGizmos.h"
#include "Editor.h"
#include <Components/Light.h>
#include <Components/MeshRenderer.h>
#include <imgui.h>
#include <iostream>

namespace Editor {

// Static member definitions
int EditorGizmos::s_selectedGameObjectIndex = -1;
bool EditorGizmos::s_hasHit = false;
glm::vec3 EditorGizmos::s_hitPoint = glm::vec3(0.0f);

// Scene view information
float EditorGizmos::s_sceneViewX = 0.0f;
float EditorGizmos::s_sceneViewY = 0.0f;
float EditorGizmos::s_sceneViewWidth = 1280.0f;
float EditorGizmos::s_sceneViewHeight = 720.0f;

// Gizmo interaction state
EditorGizmos::GizmoAxis EditorGizmos::s_activeGizmoAxis = EditorGizmos::GizmoAxis::NONE;
bool EditorGizmos::s_isDragging = false;
glm::vec3 EditorGizmos::s_dragStartPos = glm::vec3(0.0f);
glm::vec3 EditorGizmos::s_objectStartPos = glm::vec3(0.0f);

// Color picking framebuffer
std::unique_ptr<FrameBuffer> EditorGizmos::s_pickingFrameBuffer = nullptr;

EditorGizmos::GizmoSphere EditorGizmos::s_wireSphere;
EditorGizmos::GizmoBox EditorGizmos::s_wireBox;
EditorGizmos::GizmoArrow EditorGizmos::s_wireArrow;
EditorGizmos::GizmoPlane EditorGizmos::s_plane;

GLuint EditorGizmos::s_billboardVAO = 0;
GLuint EditorGizmos::s_billboardVBO = 0;
GLuint EditorGizmos::s_billboardEBO = 0;
GLuint EditorGizmos::s_gridTextureID = 0;

void EditorGizmos::Initialize()
{
    // Create gizmo geometry
    s_wireSphere = CreateWireSphere(64);
    s_wireBox = CreateWireBox();
    s_wireArrow = CreateWireArrow();
    s_plane = CreatePlane();
    
    // Create grid texture
    CreateGridTexture();
    
    // Initialize billboard geometry
    InitializeBillboardGeometry();

    // Create picking framebuffer for gizmo interaction
    s_pickingFrameBuffer = std::make_unique<FrameBuffer>(1280, 720, FrameBuffer::FrameBufferType::COLOR);
}

void EditorGizmos::Shutdown()
{
    // Clean up gizmo geometry
    if (s_wireSphere.vao) glDeleteVertexArrays(1, &s_wireSphere.vao);
    if (s_wireSphere.vbo) glDeleteBuffers(1, &s_wireSphere.vbo);
    if (s_wireSphere.ebo) glDeleteBuffers(1, &s_wireSphere.ebo);
    
    if (s_wireBox.vao) glDeleteVertexArrays(1, &s_wireBox.vao);
    if (s_wireBox.vbo) glDeleteBuffers(1, &s_wireBox.vbo);
    if (s_wireBox.ebo) glDeleteBuffers(1, &s_wireBox.ebo);
    
    if (s_wireArrow.vao) glDeleteVertexArrays(1, &s_wireArrow.vao);
    if (s_wireArrow.vbo) glDeleteBuffers(1, &s_wireArrow.vbo);
    if (s_wireArrow.eboLines) glDeleteBuffers(1, &s_wireArrow.eboLines);
    if (s_wireArrow.eboTris) glDeleteBuffers(1, &s_wireArrow.eboTris);
    
    // Clean up billboard geometry
    if (s_billboardVAO) glDeleteVertexArrays(1, &s_billboardVAO);
    if (s_billboardVBO) glDeleteBuffers(1, &s_billboardVBO);
    if (s_billboardEBO) glDeleteBuffers(1, &s_billboardEBO);
    
    // Clean up grid texture
    if (s_gridTextureID) glDeleteTextures(1, &s_gridTextureID);

    // Clean up picking framebuffer
    s_pickingFrameBuffer.reset();
}

void EditorGizmos::RenderGizmos(Camera* camera)
{
    if (!camera) return;

    // Only render to color picking framebuffer if we have a selected object with transform gizmos
    if (s_selectedGameObjectIndex >= 0)
    {
        RenderGizmosForPicking(camera);
    }

    // Handle gizmo interaction (dragging) first
    if (s_isDragging)
    {
        UpdateGizmoDrag(camera);
    }
    else
    {
        // Handle gizmo interaction BEFORE object picking so gizmos take priority
        HandleGizmoInteraction(camera);

        // Only do object picking if no gizmo was clicked
        if (s_activeGizmoAxis == GizmoAxis::NONE)
        {
            HandleMousePicking(camera);
        }
    }

    // Calculate view and projection matrices
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);

    float aspectRatio = (float)camera->m_pFrameBuffer->GetWidth() / (float)camera->m_pFrameBuffer->GetHeight();
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);

    // Disable depth testing so gizmos always render on top
    glDisable(GL_DEPTH_TEST);

    // Render all gizmo components (visual rendering)
    RenderLightGizmos(camera);
    RenderTransformGizmos(camera);
    RenderHitPointGizmo(camera);

    // Render billboards for all objects
    RenderBillboards(camera);

    // Re-enable depth testing
    glEnable(GL_DEPTH_TEST);
}

void EditorGizmos::SetSelectedGameObjectIndex(int index)
{
    s_selectedGameObjectIndex = index;

    // Sync with Editor state
    EditorState::GetInstance().selectedGameObjectIndex = index;
}

void EditorGizmos::SetSceneViewInfo(float x, float y, float width, float height)
{
    s_sceneViewX = x;
    s_sceneViewY = y;
    s_sceneViewWidth = width;
    s_sceneViewHeight = height;
}

EditorGizmos::GizmoAxis EditorGizmos::GetHoveredAxis(Camera* camera)
{
    // Only check for hover when an object is selected
    if (s_selectedGameObjectIndex < 0)
        return GizmoAxis::NONE;

    // Don't check hover if we're currently dragging
    if (s_isDragging)
        return GizmoAxis::NONE;

    // Don't check hover if scene is not hovered
    if (!EditorState::GetInstance().bIsSceneEditorHovered)
        return GizmoAxis::NONE;

    // Don't check hover if any mouse button is pressed
    if (ImGui::IsAnyMouseDown())
        return GizmoAxis::NONE;

    auto scene = EditorState::GetInstance().GetActiveScene();
    if (!scene) return GizmoAxis::NONE;

    auto gameObjects = scene->GetGameObjects();
    if (s_selectedGameObjectIndex >= static_cast<int>(gameObjects.size()))
        return GizmoAxis::NONE;

    ImVec2 mousePos = ImGui::GetMousePos();

    // Convert mouse position to scene view relative coordinates
    float relativeX = mousePos.x - s_sceneViewX;
    float relativeY = mousePos.y - s_sceneViewY;

    // Check if mouse is within scene view bounds
    if (relativeX < 0 || relativeX > s_sceneViewWidth ||
        relativeY < 0 || relativeY > s_sceneViewHeight) {
        return GizmoAxis::NONE;
    }

    // Ensure we have a valid picking framebuffer
    if (!s_pickingFrameBuffer) {
        return GizmoAxis::NONE;
    }

    // Store current framebuffer binding to restore it later
    GLint currentFramebuffer;
    glGetIntegerv(GL_FRAMEBUFFER_BINDING, &currentFramebuffer);

    s_pickingFrameBuffer->Bind();

    // Convert to framebuffer coordinates (flip Y)
    int pixelX = static_cast<int>(relativeX);
    int pixelY = static_cast<int>(s_sceneViewHeight - relativeY); // Flip Y coordinate

    // Clamp coordinates to framebuffer bounds
    pixelX = std::max(0, std::min(pixelX, static_cast<int>(s_sceneViewWidth) - 1));
    pixelY = std::max(0, std::min(pixelY, static_cast<int>(s_sceneViewHeight) - 1));

    unsigned char pixel[4];
    glReadPixels(pixelX, pixelY, 1, 1, GL_RGBA, GL_UNSIGNED_BYTE, pixel);

    // Restore previous framebuffer binding
    glBindFramebuffer(GL_FRAMEBUFFER, currentFramebuffer);

    // Get axis from color (but don't print debug info for hover)
    return GetAxisFromPickingColor(pixel[0], pixel[1], pixel[2], false);
}

std::vector<Light*> EditorGizmos::GetLights()
{
    std::vector<Light*> lights;
    
    auto scene = EditorState::GetInstance().GetActiveScene();
    if (!scene) return lights;
    
    auto gameObjects = scene->GetGameObjects();
    for (auto& gameObject : gameObjects)
    {
        auto lightComponent = gameObject->GetComponent<Light>();
        if (lightComponent)
        {
            lights.push_back(lightComponent.get());
        }
    }
    
    return lights;
}

void EditorGizmos::CreateGridTexture()
{
    // Create 256x256 texture with orange base and white grid
    const int size = 256;
    const int gridSpacing = 32;
    std::vector<unsigned char> data(size * size * 3);
    
    for (int y = 0; y < size; ++y)
    {
        for (int x = 0; x < size; ++x)
        {
            int index = (y * size + x) * 3;
            
            // Check if we're on a grid line
            bool isGridLine = (x % gridSpacing == 0) || (y % gridSpacing == 0);
            
            if (isGridLine)
            {
                // White grid lines
                data[index] = 255;     // R
                data[index + 1] = 255; // G
                data[index + 2] = 255; // B
            }
            else
            {
                // Orange background
                data[index] = 255;     // R
                data[index + 1] = 165; // G
                data[index + 2] = 0;   // B
            }
        }
    }
    
    glGenTextures(1, &s_gridTextureID);
    glBindTexture(GL_TEXTURE_2D, s_gridTextureID);
    
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, size, size, 0, GL_RGB, GL_UNSIGNED_BYTE, data.data());
    
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    
    glBindTexture(GL_TEXTURE_2D, 0);
}

void EditorGizmos::InitializeBillboardGeometry()
{
    float billboardVertices[] = {
        // Position     // TexCoord
        -0.5f, -0.5f, 0.0f,  0.0f, 0.0f,
         0.5f, -0.5f, 0.0f,  1.0f, 0.0f,
         0.5f,  0.5f, 0.0f,  1.0f, 1.0f,
        -0.5f,  0.5f, 0.0f,  0.0f, 1.0f
    };
    
    unsigned int billboardIndices[] = {
        0, 1, 2,
        2, 3, 0
    };
    
    glGenVertexArrays(1, &s_billboardVAO);
    glGenBuffers(1, &s_billboardVBO);
    glGenBuffers(1, &s_billboardEBO);
    
    glBindVertexArray(s_billboardVAO);
    
    glBindBuffer(GL_ARRAY_BUFFER, s_billboardVBO);
    glBufferData(GL_ARRAY_BUFFER, sizeof(billboardVertices), billboardVertices, GL_STATIC_DRAW);
    
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, s_billboardEBO);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, sizeof(billboardIndices), billboardIndices, GL_STATIC_DRAW);
    
    // Position attribute
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);
    
    // Texture coordinate attribute
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(3 * sizeof(float)));
    glEnableVertexAttribArray(1);
    
    glBindVertexArray(0);
}

EditorGizmos::GizmoSphere EditorGizmos::CreateWireSphere(int segments)
{
    GizmoSphere sphere;
    std::vector<glm::vec3> vertices;
    std::vector<unsigned int> indices;

    // Create 3 circles to represent light radius
    // Circle 1: XY plane (around Z axis)
    for (int i = 0; i <= segments; ++i)
    {
        float theta = 2.0f * glm::pi<float>() * i / segments;
        float x = cos(theta);
        float y = sin(theta);
        float z = 0.0f;
        vertices.push_back(glm::vec3(x, y, z));
    }

    // Circle 2: XZ plane (around Y axis)
    for (int i = 0; i <= segments; ++i)
    {
        float theta = 2.0f * glm::pi<float>() * i / segments;
        float x = cos(theta);
        float y = 0.0f;
        float z = sin(theta);
        vertices.push_back(glm::vec3(x, y, z));
    }

    // Circle 3: YZ plane (around X axis)
    for (int i = 0; i <= segments; ++i)
    {
        float theta = 2.0f * glm::pi<float>() * i / segments;
        float x = 0.0f;
        float y = cos(theta);
        float z = sin(theta);
        vertices.push_back(glm::vec3(x, y, z));
    }

    // Generate indices for the 3 circles
    // Circle 1 (XY plane)
    for (int i = 0; i < segments; ++i)
    {
        indices.push_back(i);
        indices.push_back(i + 1);
    }

    // Circle 2 (XZ plane)
    int circle2Start = segments + 1;
    for (int i = 0; i < segments; ++i)
    {
        indices.push_back(circle2Start + i);
        indices.push_back(circle2Start + i + 1);
    }

    // Circle 3 (YZ plane)
    int circle3Start = 2 * (segments + 1);
    for (int i = 0; i < segments; ++i)
    {
        indices.push_back(circle3Start + i);
        indices.push_back(circle3Start + i + 1);
    }

    glGenVertexArrays(1, &sphere.vao);
    glGenBuffers(1, &sphere.vbo);
    glGenBuffers(1, &sphere.ebo);

    glBindVertexArray(sphere.vao);

    glBindBuffer(GL_ARRAY_BUFFER, sphere.vbo);
    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(glm::vec3), vertices.data(), GL_STATIC_DRAW);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, sphere.ebo);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(unsigned int), indices.data(), GL_STATIC_DRAW);

    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(glm::vec3), (void*)0);
    glEnableVertexAttribArray(0);

    glBindVertexArray(0);

    sphere.indexCount = indices.size();
    return sphere;
}

EditorGizmos::GizmoBox EditorGizmos::CreateWireBox()
{
    GizmoBox box;

    float vertices[] = {
        // Bottom face
        -0.5f, -0.5f, -0.5f,
         0.5f, -0.5f, -0.5f,
         0.5f, -0.5f,  0.5f,
        -0.5f, -0.5f,  0.5f,
        // Top face
        -0.5f,  0.5f, -0.5f,
         0.5f,  0.5f, -0.5f,
         0.5f,  0.5f,  0.5f,
        -0.5f,  0.5f,  0.5f
    };

    unsigned int indices[] = {
        // Bottom face
        0, 1, 1, 2, 2, 3, 3, 0,
        // Top face
        4, 5, 5, 6, 6, 7, 7, 4,
        // Vertical edges
        0, 4, 1, 5, 2, 6, 3, 7
    };

    glGenVertexArrays(1, &box.vao);
    glGenBuffers(1, &box.vbo);
    glGenBuffers(1, &box.ebo);

    glBindVertexArray(box.vao);

    glBindBuffer(GL_ARRAY_BUFFER, box.vbo);
    glBufferData(GL_ARRAY_BUFFER, sizeof(vertices), vertices, GL_STATIC_DRAW);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, box.ebo);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, sizeof(indices), indices, GL_STATIC_DRAW);

    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 3 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);

    glBindVertexArray(0);

    box.indexCount = sizeof(indices) / sizeof(unsigned int);
    return box;
}

EditorGizmos::GizmoArrow EditorGizmos::CreateWireArrow()
{
    GizmoArrow arrow;

    // Create arrow with position and normal data
    std::vector<float> vertices;

    // Arrow shaft (cylinder from origin to 0.8)
    float shaftRadius = 0.02f;
    float shaftHeight = 0.8f;
    int shaftSegments = 8;

    // Shaft vertices (bottom circle)
    for (int i = 0; i <= shaftSegments; ++i) {
        float angle = 2.0f * glm::pi<float>() * i / shaftSegments;
        float x = shaftRadius * cos(angle);
        float z = shaftRadius * sin(angle);

        // Position
        vertices.push_back(x);
        vertices.push_back(0.0f);
        vertices.push_back(z);
        // Normal
        vertices.push_back(x / shaftRadius);
        vertices.push_back(0.0f);
        vertices.push_back(z / shaftRadius);
    }

    // Shaft vertices (top circle)
    for (int i = 0; i <= shaftSegments; ++i) {
        float angle = 2.0f * glm::pi<float>() * i / shaftSegments;
        float x = shaftRadius * cos(angle);
        float z = shaftRadius * sin(angle);

        // Position
        vertices.push_back(x);
        vertices.push_back(shaftHeight);
        vertices.push_back(z);
        // Normal
        vertices.push_back(x / shaftRadius);
        vertices.push_back(0.0f);
        vertices.push_back(z / shaftRadius);
    }

    // Arrow head base (larger circle at shaft top)
    float headRadius = 0.08f;
    float headHeight = 0.2f;

    for (int i = 0; i <= shaftSegments; ++i) {
        float angle = 2.0f * glm::pi<float>() * i / shaftSegments;
        float x = headRadius * cos(angle);
        float z = headRadius * sin(angle);

        // Position
        vertices.push_back(x);
        vertices.push_back(shaftHeight);
        vertices.push_back(z);
        // Normal (pointing outward and slightly up)
        glm::vec3 normal = glm::normalize(glm::vec3(x, headHeight * 0.5f, z));
        vertices.push_back(normal.x);
        vertices.push_back(normal.y);
        vertices.push_back(normal.z);
    }

    // Arrow head tip
    vertices.push_back(0.0f);  // x
    vertices.push_back(1.0f);  // y
    vertices.push_back(0.0f);  // z
    vertices.push_back(0.0f);  // normal x
    vertices.push_back(1.0f);  // normal y
    vertices.push_back(0.0f);  // normal z

    // Create line indices for wireframe
    std::vector<unsigned int> lineIndices;

    // Shaft outline (vertical lines)
    for (int i = 0; i < shaftSegments; ++i) {
        lineIndices.push_back(i);
        lineIndices.push_back(i + shaftSegments + 1);
    }

    // Head outline lines
    int headBaseStart = 2 * (shaftSegments + 1);
    int tipIndex = headBaseStart + shaftSegments + 1;

    for (int i = 0; i < shaftSegments; ++i) {
        // Lines from head base to tip
        lineIndices.push_back(headBaseStart + i);
        lineIndices.push_back(tipIndex);
    }

    // Create triangle indices for solid rendering
    std::vector<unsigned int> triIndices;

    // Shaft triangles
    for (int i = 0; i < shaftSegments; ++i) {
        int bottom1 = i;
        int bottom2 = (i + 1) % (shaftSegments + 1);
        int top1 = i + shaftSegments + 1;
        int top2 = ((i + 1) % (shaftSegments + 1)) + shaftSegments + 1;

        // Two triangles per shaft segment
        triIndices.push_back(bottom1);
        triIndices.push_back(top1);
        triIndices.push_back(bottom2);

        triIndices.push_back(bottom2);
        triIndices.push_back(top1);
        triIndices.push_back(top2);
    }

    // Head triangles (cone)
    for (int i = 0; i < shaftSegments; ++i) {
        int base1 = headBaseStart + i;
        int base2 = headBaseStart + ((i + 1) % (shaftSegments + 1));

        triIndices.push_back(base1);
        triIndices.push_back(tipIndex);
        triIndices.push_back(base2);
    }

    glGenVertexArrays(1, &arrow.vao);
    glGenBuffers(1, &arrow.vbo);
    glGenBuffers(1, &arrow.eboLines);
    glGenBuffers(1, &arrow.eboTris);

    glBindVertexArray(arrow.vao);

    glBindBuffer(GL_ARRAY_BUFFER, arrow.vbo);
    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(float), vertices.data(), GL_STATIC_DRAW);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, arrow.eboLines);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, lineIndices.size() * sizeof(unsigned int), lineIndices.data(), GL_STATIC_DRAW);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, arrow.eboTris);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, triIndices.size() * sizeof(unsigned int), triIndices.data(), GL_STATIC_DRAW);

    // Position attribute (location 0)
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);

    // Normal attribute (location 1)
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 6 * sizeof(float), (void*)(3 * sizeof(float)));
    glEnableVertexAttribArray(1);

    glBindVertexArray(0);

    arrow.lineIndexCount = lineIndices.size();
    arrow.triIndexCount = triIndices.size();
    return arrow;
}

EditorGizmos::GizmoPlane EditorGizmos::CreatePlane()
{
    GizmoPlane plane;

    // Create a simple quad for plane handles
    float vertices[] = {
        // Position coordinates for a unit quad in XY plane
        0.0f, 0.0f, 0.0f,  // Bottom-left
        0.3f, 0.0f, 0.0f,  // Bottom-right
        0.3f, 0.3f, 0.0f,  // Top-right
        0.0f, 0.3f, 0.0f   // Top-left
    };

    unsigned int indices[] = {
        0, 1, 2,  // First triangle
        0, 2, 3   // Second triangle
    };

    glGenVertexArrays(1, &plane.vao);
    glGenBuffers(1, &plane.vbo);
    glGenBuffers(1, &plane.ebo);

    glBindVertexArray(plane.vao);

    glBindBuffer(GL_ARRAY_BUFFER, plane.vbo);
    glBufferData(GL_ARRAY_BUFFER, sizeof(vertices), vertices, GL_STATIC_DRAW);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, plane.ebo);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, sizeof(indices), indices, GL_STATIC_DRAW);

    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 3 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);

    glBindVertexArray(0);

    plane.indexCount = sizeof(indices) / sizeof(unsigned int);
    return plane;
}

} // namespace Editor
