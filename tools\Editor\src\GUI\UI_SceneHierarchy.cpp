#include <glen.h>
#include <imgui.h>
#include <imgui_internal.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include "../Editor/Editor.h"

static int selectedGameObjectIndex = -1;
static void DrawGameObjectNode(const std::shared_ptr<GameObject>& entity);
static void RenderSceneHierarchyContextMenu();

void RenderSceneHierarchyWindow()
{
    ImGui::Begin("Scene Hierarchy");

    Scene *activeScene = Renderer::s_pActiveScene;
    if (activeScene)
    {
        const auto &gameObjects = activeScene->GetGameObjects();
        for (size_t i = 0; i < gameObjects.size(); ++i)
        {
            const auto &entity = gameObjects[i];
            // Only draw root objects (no parent)
            if (entity->GetParent() == nullptr)
            {
                DrawGameObjectNode(entity);
            }
        }
    }

    ImGui::End();
}

void DrawGameObjectNode(const std::shared_ptr<GameObject>& entity)
{
    ImGuiTreeNodeFlags flags = ImGuiTreeNodeFlags_OpenOnArrow | ImGuiTreeNodeFlags_OpenOnDoubleClick;

    // Check if this entity is the currently selected one
    auto& editorState = Editor::EditorState::GetInstance();
    if (editorState.GetActiveGameObject() == entity)
        flags |= ImGuiTreeNodeFlags_Selected;

    bool hasChildren = !entity->GetChildren().empty();
    bool open = ImGui::TreeNodeEx(entity->GetName().c_str(), flags | (hasChildren ? 0 : ImGuiTreeNodeFlags_Leaf));

    // Handle selection
    if (ImGui::IsItemClicked())
    {
        // Set the active GameObject directly
        editorState.SetActiveGameObject(entity);

        // Also update the selectedGameObjectIndex for backward compatibility
        // Find the index of this GameObject in the scene's root objects list
        Scene *activeScene = Renderer::s_pActiveScene;
        if (activeScene)
        {
            const auto &gameObjects = activeScene->GetGameObjects();
            editorState.selectedGameObjectIndex = -1; // Default to -1 for child objects

            for (size_t i = 0; i < gameObjects.size(); ++i)
            {
                if (gameObjects[i] == entity)
                {
                    editorState.selectedGameObjectIndex = static_cast<int>(i);
                    break;
                }
            }
        }
    }

    // Context menu for the entity
    RenderSceneHierarchyContextMenu();

    if (open && hasChildren)
    {
        const auto& children = entity->GetChildren();
        for (const auto& child : children)
        {
            DrawGameObjectNode(child);
        }
        ImGui::TreePop();
    }
    else if (open)
    {
        ImGui::TreePop();
    }
}

void RenderSceneHierarchyContextMenu()
{
    if (ImGui::BeginPopupContextWindow())
    {
        if (ImGui::MenuItem("Create Empty"))
        {
            auto newGameObject = std::make_shared<GameObject>("GameObject");

            auto SoundComponent = std::make_shared<Sound>("test");
            SoundComponent->SetVolume(100.0f);
            SoundComponent->SetLoop(true);
            SoundComponent->Set3D(false);
            newGameObject->AddComponent(SoundComponent);
            Scene *activeScene = Renderer::s_pActiveScene;
            if (activeScene)
            {
                activeScene->AddGameObject(newGameObject);
                // Clear selection
                auto& editorState = Editor::EditorState::GetInstance();
                editorState.SetActiveGameObject(nullptr);
                editorState.selectedGameObjectIndex = -1;
            }
        }
        if (ImGui::MenuItem("Delete"))
        {
            auto& editorState = Editor::EditorState::GetInstance();
            auto selectedObject = editorState.GetActiveGameObject();
            if (selectedObject)
            {
                Scene *activeScene = Renderer::s_pActiveScene;
                if (activeScene)
                {
                    // Check if it's a root object (can be removed from scene)
                    if (selectedObject->GetParent() == nullptr)
                    {
                        activeScene->RemoveGameObject(selectedObject);
                    }
                    else
                    {
                        // It's a child object, remove it from its parent
                        auto parent = selectedObject->GetParent();
                        // Note: We need to implement RemoveChild method in GameObject
                        // For now, this will need to be implemented
                    }

                    // Clear selection
                    editorState.SetActiveGameObject(nullptr);
                    editorState.selectedGameObjectIndex = -1;
                }
            }
        }
        ImGui::EndPopup();
    }
}
