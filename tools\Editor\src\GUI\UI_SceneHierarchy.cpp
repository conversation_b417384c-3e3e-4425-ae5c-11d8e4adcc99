#include <glen.h>
#include <imgui.h>
#include <imgui_internal.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include "../Editor/Editor.h"

static int selectedGameObjectIndex = -1;
static void DrawGameObjectNode(const std::shared_ptr<GameObject>& entity, int index);
static void RenderSceneHierarchyContextMenu();

void RenderSceneHierarchyWindow()
{
    ImGui::Begin("Scene Hierarchy");

    Scene *activeScene = Renderer::s_pActiveScene;
    if (activeScene)
    {
        const auto &gameObjects = activeScene->GetGameObjects();
        for (size_t i = 0; i < gameObjects.size(); ++i)
        {
            const auto &entity = gameObjects[i];
            // Only draw root objects (no parent)
            if (entity->GetParent() == nullptr)
            {
                DrawGameObjectNode(entity, static_cast<int>(i));
            }
        }
    }

    ImGui::End();
}

void DrawGameObjectNode(const std::shared_ptr<GameObject>& entity, int index)
{
    ImGuiTreeNodeFlags flags = ImGuiTreeNodeFlags_OpenOnArrow | ImGuiTreeNodeFlags_OpenOnDoubleClick;

    if (Editor::EditorState::GetInstance().selectedGameObjectIndex == index)
        flags |= ImGuiTreeNodeFlags_Selected;

    bool hasChildren = !entity->GetChildren().empty();
    bool open = ImGui::TreeNodeEx(entity->GetName().c_str(), flags | (hasChildren ? 0 : ImGuiTreeNodeFlags_Leaf));

    // Handle selection
    if (ImGui::IsItemClicked())
    {
        Editor::EditorState::GetInstance().selectedGameObjectIndex = index;
    }

    // Context menu for the entity
    RenderSceneHierarchyContextMenu();

    if (open && hasChildren)
    {
        const auto& children = entity->GetChildren();
        for (const auto& child : children)
        {
            // You may want to get the index of the child in your scene's game object list if needed
            DrawGameObjectNode(child, index); // Pass appropriate index if you want selection to work per-child
        }
        ImGui::TreePop();
    }
    else if (open)
    {
        ImGui::TreePop();
    }
}

void RenderSceneHierarchyContextMenu()
{
    if (ImGui::BeginPopupContextWindow())
    {
        if (ImGui::MenuItem("Create Empty"))
        {

            auto newGameObject = std::make_shared<GameObject>("GameObject");

            auto SoundComponent = std::make_shared<Sound>("test");
            SoundComponent->SetVolume(100.0f);
            SoundComponent->SetLoop(true);
            SoundComponent->Set3D(false);
            newGameObject->AddComponent(SoundComponent);
            Scene *activeScene = Renderer::s_pActiveScene;
            if (activeScene)
            {
                activeScene->AddGameObject(newGameObject);
                Editor::EditorState::GetInstance().selectedGameObjectIndex = -1; // Reset selection
            }

        }
        if (ImGui::MenuItem("Delete"))
        {
            if (Editor::EditorState::GetInstance().selectedGameObjectIndex >= 0)
            {
                Scene *activeScene = Renderer::s_pActiveScene;
                if (activeScene)
                {
                    const auto &gameObjects = activeScene->GetGameObjects();
                    if (Editor::EditorState::GetInstance().selectedGameObjectIndex < static_cast<int>(gameObjects.size()))
                    {
                        activeScene->RemoveGameObject(gameObjects[Editor::EditorState::GetInstance().selectedGameObjectIndex]);
                        Editor::EditorState::GetInstance().selectedGameObjectIndex = -1; // Reset selection
                    }
                }
            }
        }
        ImGui::EndPopup();
    }
}
